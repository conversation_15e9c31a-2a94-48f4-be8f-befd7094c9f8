import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { DeveloperManagementService } from '../common/services/developer-management.service';
import { RepositorySyncService } from '../common/services/repository-sync.service';
import { DeveloperController } from './developer.controller';

describe('DeveloperController Unit Tests - Line 203 Coverage', () => {
    let controller: DeveloperController;
    let developerManagementService: jest.Mocked<DeveloperManagementService>;

    const mockUser = { id: 'user-123' } as any;

    beforeEach(async () => {
        const mockDeveloperManagementService = {
            syncDeveloperProfile: jest.fn(),
        };

        const mockRepositorySyncService = {};

        const module: TestingModule = await Test.createTestingModule({
            controllers: [DeveloperController],
            providers: [
                {
                    provide: DeveloperManagementService,
                    useValue: mockDeveloperManagementService,
                },
                {
                    provide: RepositorySyncService,
                    useValue: mockRepositorySyncService,
                },
            ],
        }).compile();

        controller = module.get<DeveloperController>(DeveloperController);
        developerManagementService = module.get(DeveloperManagementService);
    });

    describe('syncDeveloperProfile - Generic Error Handler Coverage', () => {
        it('should handle generic error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Mock a generic error that does NOT contain "not found" to trigger the generic error handler
            // This will hit the generic BadRequestException throw on line 203
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Generic sync error - database connection failed')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            // Verify the specific error message format from line 203
            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Generic sync error - database connection failed');
            }
        });

        it('should handle timeout error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Another test case to ensure line 203 is covered with different error types
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Service timeout exceeded')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Service timeout exceeded');
            }
        });

        it('should handle network error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Third test case to thoroughly cover the generic error handler
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Network error: unable to reach external service')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Network error: unable to reach external service');
            }
        });
    });
}); 