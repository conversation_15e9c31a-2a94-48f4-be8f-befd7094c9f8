import { MailerModule } from '@nestjs-modules/mailer';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import { EmailManagementModule } from './email-management.module.js';
import { EmailService } from './email.service.js';

// Handle both ES modules and CommonJS for compatibility with Jest
const getModuleDir = () => {
  try {
    // Try to get ES module path using dynamic evaluation to avoid TS compilation issues
    const importMeta = eval('import.meta');
    if (importMeta && importMeta.url) {
      const __filename = fileURLToPath(importMeta.url);
      return dirname(__filename);
    }
  } catch {
    // Fall through to CommonJS approach
  }

  // CommonJS fallback for Jest/testing - use the global __dirname if available
  if (typeof __dirname !== 'undefined') {
    return __dirname;
  }

  // Final fallback - use current working directory
  return process.cwd();
};

const moduleDir = getModuleDir();

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const isTest = configService.get('NODE_ENV') === 'test';

        const config: any = {
          transport: {
            host: configService.get('SMTP_HOST'),
            port: parseInt(configService.get('SMTP_PORT', '587'), 10),
            secure: configService.get('SMTP_SECURE') === 'true',
            auth: {
              user: configService.get('SMTP_USER'),
              pass: configService.get('SMTP_PASSWORD'),
            },
            // Additional options for development
            ignoreTLS: configService.get('NODE_ENV') === 'development',
            requireTLS: configService.get('NODE_ENV') === 'production',
          },
          defaults: {
            from: `"${configService.get('EMAIL_FROM_NAME', 'RSGlider')}" <${configService.get('SMTP_FROM', '<EMAIL>')}>`,
          },
          // Preview emails in development
          preview: configService.get('NODE_ENV') === 'development',
        };

        // Only load HandlebarsAdapter when not in test mode to avoid Jest open handles
        if (!isTest) {
          const { HandlebarsAdapter } = await import('@nestjs-modules/mailer/dist/adapters/handlebars.adapter.js');
          config.template = {
            dir: join(moduleDir, 'templates'),
            adapter: new HandlebarsAdapter({
              // Handlebars helpers
              helpers: {
                // Format date helper
                formatDate: function (date: any) {
                  return new Intl.DateTimeFormat('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  }).format(new Date(date));
                },
                // Conditional helper
                ifEquals: function (arg1: any, arg2: any, options: any) {
                  return (arg1 === arg2) ? options.fn(this) : options.inverse(this);
                },
                // URL helper for generating links
                url: function (path: any) {
                  const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
                  return `${baseUrl}${path}`;
                },
              } as any,
            }),
            options: {
              strict: true,
            },
          };
        }

        return config;
      },
      inject: [ConfigService],
    }),
    forwardRef(() => EmailManagementModule),
  ],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule { }
