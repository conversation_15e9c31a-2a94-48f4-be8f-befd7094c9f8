#!/bin/bash

# Reset Gitea Admin Password Script
# This script resets the admin password and creates a new API token

set -e

echo "🔧 Resetting Gitea Admin Password"

# Configuration
ADMIN_USERNAME="admin"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="rsglider_admin_password_change_in_production"
NEW_PASSWORD="admin123"  # Temporary simple password for testing
TOKEN_NAME="rsglider_api_token_$(date +%s)"
SHARED_DIR="./shared"
TOKEN_FILE="$SHARED_DIR/gitea_admin_token.txt"

# Ensure shared directory exists
mkdir -p "$SHARED_DIR"

echo "📋 Using credentials:"
echo "   Username: $ADMIN_USERNAME"
echo "   Email:    $ADMIN_EMAIL"
echo "   New Password: $NEW_PASSWORD"
echo ""

# Check if Gitea container is running
echo "🔍 Checking Gitea container status..."
if ! docker ps | grep -q "rsglider-gitea"; then
    echo "❌ Gitea container is not running. Please start it first:"
    echo "   docker-compose up -d gitea"
    exit 1
fi

echo "✅ Gitea container is running"

# Reset admin password using Gitea CLI inside the container
echo "🔑 Resetting admin password..."
docker exec rsglider-gitea gitea admin user change-password \
    --username "$ADMIN_USERNAME" \
    --password "$NEW_PASSWORD" \
    --must-change-password=false || {
    echo "⚠️  Password reset via CLI failed, trying to create user..."
    
    # Try to create the admin user if it doesn't exist
    docker exec rsglider-gitea gitea admin user create \
        --username "$ADMIN_USERNAME" \
        --email "$ADMIN_EMAIL" \
        --password "$NEW_PASSWORD" \
        --admin \
        --must-change-password=false || {
        echo "❌ Failed to create admin user. User might already exist."
        echo "ℹ️  Try logging in with username: $ADMIN_USERNAME and password: $NEW_PASSWORD"
        exit 1
    }
}

echo "✅ Admin password reset successfully!"

# Wait a moment for changes to take effect
sleep 2

# Test login with new credentials
echo "🧪 Testing login with new credentials..."
LOGIN_TEST=$(curl -s -X POST "http://localhost:3001/user/login" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "user_name=$ADMIN_USERNAME&password=$NEW_PASSWORD" \
    -c /tmp/gitea_cookies.txt \
    -w "%{http_code}" -o /dev/null)

if [ "$LOGIN_TEST" = "302" ] || [ "$LOGIN_TEST" = "200" ]; then
    echo "✅ Login test successful!"
else
    echo "⚠️  Login test returned HTTP $LOGIN_TEST"
    echo "ℹ️  This might be normal - try logging in manually"
fi

# Generate API token
echo "🔑 Generating new API token..."
TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:3001/api/v1/users/$ADMIN_USERNAME/tokens" \
    -H "Content-Type: application/json" \
    -u "$ADMIN_USERNAME:$NEW_PASSWORD" \
    -d "{\"name\":\"$TOKEN_NAME\",\"scopes\":[\"all\"]}")

echo "📋 Token response: $TOKEN_RESPONSE"

# Extract token
if echo "$TOKEN_RESPONSE" | grep -q '"sha1"'; then
    TOKEN_RESULT=$(echo "$TOKEN_RESPONSE" | sed -n 's/.*"sha1":"\([^"]*\)".*/\1/p')
    
    if [ -n "$TOKEN_RESULT" ] && [ "$TOKEN_RESULT" != "null" ]; then
        echo "✅ API token generated successfully!"
        echo "🔑 Token: $TOKEN_RESULT"
        
        # Save token to shared file
        echo "$TOKEN_RESULT" > "$TOKEN_FILE"
        echo "💾 Token saved to $TOKEN_FILE"
        
        # Test token
        echo "🧪 Testing token..."
        TOKEN_TEST=$(curl -s -H "Authorization: token $TOKEN_RESULT" \
            "http://localhost:3001/api/v1/user" | grep -o '"login":"[^"]*"' || echo "")
        
        if [ -n "$TOKEN_TEST" ]; then
            echo "✅ Token test successful: $TOKEN_TEST"
        else
            echo "⚠️  Token test failed, but token was generated"
        fi
    else
        echo "❌ Failed to extract token from response"
    fi
else
    echo "❌ Token generation failed: $TOKEN_RESPONSE"
fi

echo ""
echo "🎉 Gitea admin reset completed!"
echo ""
echo "📋 New Admin Credentials:"
echo "   Username: $ADMIN_USERNAME"
echo "   Password: $NEW_PASSWORD"
echo "   Web UI:   http://localhost:3001"
echo "   Token:    $(cat "$TOKEN_FILE" 2>/dev/null || echo "Not generated")"
echo ""
echo "🔗 Try logging in at: http://localhost:3001/user/login"
echo ""
echo "⚠️  Remember to change the password after logging in!"
