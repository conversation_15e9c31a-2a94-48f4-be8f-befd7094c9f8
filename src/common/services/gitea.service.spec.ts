import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import * as fs from 'fs';
import { GiteaService } from './gitea.service';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock fs
jest.mock('fs');
const mockedFs = fs as jest.Mocked<typeof fs>;

describe('GiteaService', () => {
  let service: GiteaService;
  let configService: jest.Mocked<ConfigService>;
  let mockAxiosInstance: jest.Mocked<ReturnType<typeof axios.create>>;

  const mockConfig = {
    GITEA_BASE_URL: 'http://localhost:3001',
    GITEA_EXTERNAL_URL: 'http://localhost:3001',
    GITEA_ADMIN_TOKEN: 'test-admin-token',
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock axios instance
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    } as any;

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Mock ConfigService
    const mockConfigService = {
      get: jest.fn((key: string) => mockConfig[key]),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GiteaService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GiteaService>(GiteaService);
    configService = module.get(ConfigService);
  });

  describe('constructor', () => {
    it('should initialize with environment variables', () => {
      expect(configService.get).toHaveBeenCalledWith('GITEA_BASE_URL');
      expect(configService.get).toHaveBeenCalledWith('GITEA_EXTERNAL_URL');
      expect(configService.get).toHaveBeenCalledWith('GITEA_ADMIN_TOKEN');
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:3001/api/v1',
        headers: {
          'Authorization': 'token test-admin-token',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });
    });

    it('should read token from file when environment token is default', async () => {
      // Reset and create new service with default token
      jest.clearAllMocks();
      mockedFs.existsSync.mockReturnValue(true);
      mockedFs.readFileSync.mockReturnValue('file-token-123');

      const mockConfigServiceWithDefault = {
        get: jest.fn((key: string) => {
          if (key === 'GITEA_ADMIN_TOKEN') return 'rsglider_gitea_admin_token_change_in_production';
          return mockConfig[key];
        }),
      };

      await Test.createTestingModule({
        providers: [
          GiteaService,
          {
            provide: ConfigService,
            useValue: mockConfigServiceWithDefault,
          },
        ],
      }).compile();

      expect(mockedFs.existsSync).toHaveBeenCalledWith('/app/shared/gitea_admin_token.txt');
      expect(mockedFs.readFileSync).toHaveBeenCalledWith('/app/shared/gitea_admin_token.txt', 'utf8');
    });

    it('should throw error when required config is missing', async () => {
      const mockConfigServiceMissing = {
        get: jest.fn(() => undefined),
      };

      await expect(
        Test.createTestingModule({
          providers: [
            GiteaService,
            {
              provide: ConfigService,
              useValue: mockConfigServiceMissing,
            },
          ],
        }).compile()
      ).rejects.toThrow('Gitea configuration missing: GITEA_BASE_URL and GITEA_ADMIN_TOKEN are required');
    });
  });

  describe('testConnection', () => {
    it('should return true when connection is successful', async () => {
      const mockResponse = { data: { version: '1.21.0' } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await service.testConnection();

      expect(result).toBe(true);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/version');
    });

    it('should return false when connection fails', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'));

      const result = await service.testConnection();

      expect(result).toBe(false);
    });
  });

  describe('getVersion', () => {
    it('should return version data when successful', async () => {
      const mockVersionData = { version: '1.21.0' };
      mockAxiosInstance.get.mockResolvedValue({ data: mockVersionData });

      const result = await service.getVersion();

      expect(result).toEqual(mockVersionData);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/version');
    });

    it('should return unknown version when response is invalid', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: null });

      const result = await service.getVersion();

      expect(result).toEqual({ version: 'unknown' });
    });

    it('should throw BadRequestException when request fails', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Network error'));

      await expect(service.getVersion()).rejects.toThrow(BadRequestException);
    });
  });

  describe('createUser', () => {
    const mockUserOptions = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      full_name: 'Test User',
    };

    it('should create user successfully', async () => {
      const mockResponse = { data: { id: 1, login: 'testuser' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await service.createUser(mockUserOptions);

      expect(result).toEqual(mockResponse.data);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/admin/users', {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'Test User',
        login_name: 'testuser',
        send_notify: false,
        source_id: 0,
        must_change_password: false,
        restricted: false,
        visibility: 'public',
      });
    });

    it('should throw BadRequestException when creation fails', async () => {
      const mockError = {
        response: {
          data: { message: 'User already exists' },
        },
      };
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(service.createUser(mockUserOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserByUsername', () => {
    it('should return user data when found', async () => {
      const mockUser = { id: 1, login: 'testuser', email: '<EMAIL>' };
      mockAxiosInstance.get.mockResolvedValue({ data: mockUser });

      const result = await service.getUserByUsername('testuser');

      expect(result).toEqual(mockUser);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser');
    });

    it('should throw NotFoundException when user not found', async () => {
      const mockError = { response: { status: 404 } };
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(service.getUserByUsername('nonexistent')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for other errors', async () => {
      const mockError = { response: { status: 500, data: { message: 'Server error' } } };
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(service.getUserByUsername('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateUser', () => {
    const mockUpdateOptions = { full_name: 'Updated Name' };

    it('should update user successfully', async () => {
      const mockResponse = { data: { id: 1, login: 'testuser', full_name: 'Updated Name' } };
      mockAxiosInstance.patch.mockResolvedValue(mockResponse);

      const result = await service.updateUser('testuser', mockUpdateOptions);

      expect(result).toEqual(mockResponse.data);
      expect(mockAxiosInstance.patch).toHaveBeenCalledWith('/admin/users/testuser', mockUpdateOptions);
    });

    it('should throw BadRequestException when update fails', async () => {
      mockAxiosInstance.patch.mockRejectedValue(new Error('Update failed'));

      await expect(service.updateUser('testuser', mockUpdateOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValue({});

      await service.deleteUser('testuser');

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/admin/users/testuser');
    });

    it('should throw BadRequestException when deletion fails', async () => {
      mockAxiosInstance.delete.mockRejectedValue(new Error('Deletion failed'));

      await expect(service.deleteUser('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserRepositories', () => {
    it('should return user repositories with default pagination', async () => {
      const mockRepos = [
        { id: 1, name: 'repo1', full_name: 'testuser/repo1' },
        { id: 2, name: 'repo2', full_name: 'testuser/repo2' },
      ];
      mockAxiosInstance.get.mockResolvedValue({ data: mockRepos });

      const result = await service.getUserRepositories('testuser');

      expect(result).toEqual(mockRepos);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser/repos', {
        params: { page: 1, limit: 50 },
      });
    });

    it('should return user repositories with custom pagination', async () => {
      const mockRepos = [{ id: 1, name: 'repo1' }];
      mockAxiosInstance.get.mockResolvedValue({ data: mockRepos });

      const result = await service.getUserRepositories('testuser', 2, 10);

      expect(result).toEqual(mockRepos);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser/repos', {
        params: { page: 2, limit: 10 },
      });
    });

    it('should throw BadRequestException when request fails', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Request failed'));

      await expect(service.getUserRepositories('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getRepository', () => {
    it('should return repository data when found', async () => {
      const mockRepo = { id: 1, name: 'testrepo', full_name: 'testuser/testrepo' };
      mockAxiosInstance.get.mockResolvedValue({ data: mockRepo });

      const result = await service.getRepository('testuser', 'testrepo');

      expect(result).toEqual(mockRepo);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/repos/testuser/testrepo');
    });

    it('should throw NotFoundException when repository not found', async () => {
      const mockError = { response: { status: 404 } };
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(service.getRepository('testuser', 'nonexistent')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for other errors', async () => {
      const mockError = { response: { status: 500, data: { message: 'Server error' } } };
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(service.getRepository('testuser', 'testrepo')).rejects.toThrow(BadRequestException);
    });
  });

  describe('createRepository', () => {
    const mockRepoOptions = {
      name: 'newrepo',
      description: 'A new repository',
      private: false,
    };

    it('should create repository successfully', async () => {
      const mockResponse = { data: { id: 1, name: 'newrepo', full_name: 'testuser/newrepo' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await service.createRepository('testuser', mockRepoOptions);

      expect(result).toEqual(mockResponse.data);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/admin/users/testuser/repos', {
        name: 'newrepo',
        description: 'A new repository',
        private: false,
        auto_init: true,
        gitignores: '',
        license: '',
        readme: 'Default',
        default_branch: 'main',
        trust_model: 'default',
      });
    });

    it('should throw BadRequestException when creation fails', async () => {
      const mockError = { response: { data: { message: 'Repository already exists' } } };
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(service.createRepository('testuser', mockRepoOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteRepository', () => {
    it('should delete repository successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValue({});

      await service.deleteRepository('testuser', 'testrepo');

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/repos/testuser/testrepo');
    });

    it('should throw BadRequestException when deletion fails', async () => {
      mockAxiosInstance.delete.mockRejectedValue(new Error('Deletion failed'));

      await expect(service.deleteRepository('testuser', 'testrepo')).rejects.toThrow(BadRequestException);
    });
  });

  describe('createWebhook', () => {
    const mockWebhookOptions = {
      type: 'gitea' as const,
      config: { url: 'http://example.com/webhook', content_type: 'json' as const },
      events: ['push', 'pull_request'],
      active: true,
    };

    it('should create webhook successfully', async () => {
      const mockResponse = { data: { id: 1, type: 'gitea' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await service.createWebhook('testuser', 'testrepo', mockWebhookOptions);

      expect(result).toEqual(mockResponse.data);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks', {
        type: 'gitea',
        config: { url: 'http://example.com/webhook', content_type: 'json' },
        events: ['push', 'pull_request'],
        active: true,
        branch_filter: '',
      });
    });

    it('should create webhook with default active true when not specified', async () => {
      const optionsWithoutActive = { ...mockWebhookOptions };
      delete optionsWithoutActive.active;
      const mockResponse = { data: { id: 1, type: 'gitea' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      await service.createWebhook('testuser', 'testrepo', optionsWithoutActive);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks', {
        type: 'gitea',
        config: { url: 'http://example.com/webhook', content_type: 'json' },
        events: ['push', 'pull_request'],
        active: true,
        branch_filter: '',
      });
    });

    it('should throw BadRequestException when creation fails', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Creation failed'));

      await expect(service.createWebhook('testuser', 'testrepo', mockWebhookOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getWebhooks', () => {
    it('should return webhooks for repository', async () => {
      const mockWebhooks = [
        { id: 1, type: 'gitea', active: true },
        { id: 2, type: 'slack', active: false },
      ];
      mockAxiosInstance.get.mockResolvedValue({ data: mockWebhooks });

      const result = await service.getWebhooks('testuser', 'testrepo');

      expect(result).toEqual(mockWebhooks);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks');
    });

    it('should throw BadRequestException when request fails', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Request failed'));

      await expect(service.getWebhooks('testuser', 'testrepo')).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteWebhook', () => {
    it('should delete webhook successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValue({});

      await service.deleteWebhook('testuser', 'testrepo', 123);

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks/123');
    });

    it('should throw BadRequestException when deletion fails', async () => {
      mockAxiosInstance.delete.mockRejectedValue(new Error('Deletion failed'));

      await expect(service.deleteWebhook('testuser', 'testrepo', 123)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getRepositoryExternalUrl', () => {
    it('should return correct external URL', () => {
      const result = service.getRepositoryExternalUrl('testuser', 'testrepo');

      expect(result).toBe('http://localhost:3001/testuser/testrepo');
    });
  });

  describe('getRepositoryCloneUrl', () => {
    it('should return HTTPS clone URL by default', () => {
      const result = service.getRepositoryCloneUrl('testuser', 'testrepo');

      expect(result).toBe('http://localhost:3001/testuser/testrepo.git');
    });

    it('should return HTTPS clone URL when useSSH is false', () => {
      const result = service.getRepositoryCloneUrl('testuser', 'testrepo', false);

      expect(result).toBe('http://localhost:3001/testuser/testrepo.git');
    });

    it('should return SSH clone URL when useSSH is true', () => {
      const result = service.getRepositoryCloneUrl('testuser', 'testrepo', true);

      expect(result).toBe('git@localhost:3001:testuser/testrepo.git');
    });
  });
});
