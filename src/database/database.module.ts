import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import * as schema from './schema/index.js';

@Module({
  providers: [
    {
      provide: 'DB',
      useFactory: async (configService: ConfigService) => {
        const connectionString = `postgresql://${configService.get('DATABASE_USER', 'rsglider')}:${configService.get('DATABASE_PASSWORD', 'rsglider_dev_password')}@${configService.get('DATABASE_HOST', 'postgres')}:${configService.get('DATABASE_PORT', '5432')}/${configService.get('DATABASE_NAME', 'rsglider')}`;

        const client = postgres(connectionString, {
          max: 10,
          idle_timeout: 20,
          connect_timeout: 10,
          // Only enable debug logging in development with explicit flag
          debug: process.env.NODE_ENV === 'development' && process.env.DEBUG_SQL === 'true',
        });

        // Test the connection
        await client`SELECT 1 as test`.then(
          () => console.log('✅ Database connected successfully'),
          (error) => {
            // Log connection errors safely
            if (process.env.NODE_ENV === 'production') {
              console.error('❌ Database connection failed');
            } else {
              console.error('❌ Database connection failed:', error.message);
            }
            throw error;
          }
        );

        // Configure Drizzle with conditional logging
        const db = drizzle(client, {
          schema,
          logger: process.env.NODE_ENV === 'development' && process.env.DEBUG_SQL === 'true',
        });

        // Run migrations automatically (same as tests do)
        try {
          console.log('🗄️  Running database migrations...');
          await migrate(db, { migrationsFolder: './src/database/migrations' });
          console.log('✅ Database migrations completed successfully!');
        } catch (error) {
          console.warn('⚠️  Migration warning (might be expected):', error.message);
          // Don't throw - app can still work if migrations fail
        }

        return db;
      },
      inject: [ConfigService],
    },
  ],
  exports: ['DB'],
})
export class DatabaseModule { }
