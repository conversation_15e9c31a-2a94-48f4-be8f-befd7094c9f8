// Test for index.ts file to achieve coverage
describe('Developer DTOs Index', () => {
  it('should export all DTOs', () => {
    const indexModule = require('./index');
    
    // Test that the index file can be imported without errors
    expect(indexModule).toBeDefined();
  });

  it('should have exports', () => {
    const indexModule = require('./index');
    
    // Check that exports exist (even if empty)
    expect(typeof indexModule).toBe('object');
  });
});
